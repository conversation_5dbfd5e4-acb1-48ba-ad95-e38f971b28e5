#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
content_validator.py

AI内容验证器模块
提供内容长度验证、质量检查和重试逻辑
"""

import logging
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime


@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    original_length: int
    generated_length: int
    validation_ratio: float
    reason: str = ""


@dataclass
class FailureRecord:
    """失败记录数据类"""
    chunk_idx: int
    original_content: str
    generated_content: str
    failure_reason: str
    timestamp: str
    attempt_count: int


class ContentValidator:
    """AI内容验证器"""
    
    def __init__(self, threshold: float = 0.8, max_retries: int = 2, log_dir: str = "logs"):
        """
        初始化内容验证器
        
        Args:
            threshold: 内容长度验证阈值（默认80%）
            max_retries: 最大重试次数（默认2次）
            log_dir: 日志目录
        """
        self.threshold = threshold
        self.max_retries = max_retries
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 失败记录
        self.failure_records: List[FailureRecord] = []
        
        # 统计信息
        self.stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'total_retries': 0,
            'successful_retries': 0
        }
    
    def validate_content_length(self, original: str, generated: str) -> ValidationResult:
        """
        验证生成内容的长度是否达到阈值
        
        Args:
            original: 原始输入内容
            generated: AI生成的内容
            
        Returns:
            验证结果
        """
        self.stats['total_validations'] += 1
        
        original_length = len(original.strip())
        generated_length = len(generated.strip()) if generated else 0
        
        # 避免除零错误
        if original_length == 0:
            ratio = 1.0 if generated_length == 0 else 0.0
        else:
            ratio = generated_length / original_length
        
        is_valid = ratio >= self.threshold
        
        if is_valid:
            self.stats['passed_validations'] += 1
            reason = f"内容长度验证通过 ({ratio:.2%})"
        else:
            self.stats['failed_validations'] += 1
            reason = f"内容长度不足，仅为原始内容的 {ratio:.2%}，低于阈值 {self.threshold:.2%}"
        
        return ValidationResult(
            is_valid=is_valid,
            original_length=original_length,
            generated_length=generated_length,
            validation_ratio=ratio,
            reason=reason
        )
    
    def validate_content_quality(self, original: str, generated: str) -> ValidationResult:
        """
        验证生成内容的质量
        
        Args:
            original: 原始输入内容
            generated: AI生成的内容
            
        Returns:
            验证结果
        """
        # 基础长度验证
        length_result = self.validate_content_length(original, generated)
        
        if not length_result.is_valid:
            return length_result
        
        # 额外的质量检查
        if not generated or not generated.strip():
            return ValidationResult(
                is_valid=False,
                original_length=length_result.original_length,
                generated_length=0,
                validation_ratio=0.0,
                reason="生成内容为空"
            )
        
        # 检查是否包含基本的JSON结构（针对问答对生成）
        if '"front"' not in generated and '"back"' not in generated:
            return ValidationResult(
                is_valid=False,
                original_length=length_result.original_length,
                generated_length=length_result.generated_length,
                validation_ratio=length_result.validation_ratio,
                reason="生成内容缺少预期的JSON结构"
            )
        
        return ValidationResult(
            is_valid=True,
            original_length=length_result.original_length,
            generated_length=length_result.generated_length,
            validation_ratio=length_result.validation_ratio,
            reason="内容质量验证通过"
        )
    
    def should_retry(self, attempt: int, validation_result: ValidationResult) -> bool:
        """
        判断是否应该重试
        
        Args:
            attempt: 当前尝试次数（从0开始）
            validation_result: 验证结果
            
        Returns:
            是否应该重试
        """
        if validation_result.is_valid:
            return False
        
        if attempt >= self.max_retries:
            return False
        
        # 如果内容完全为空，值得重试
        if validation_result.generated_length == 0:
            return True
        
        # 如果内容长度严重不足（低于50%），值得重试
        if validation_result.validation_ratio < 0.5:
            return True
        
        return True
    
    def log_failure(self, chunk_idx: int, original: str, generated: str, 
                   reason: str, attempt_count: int):
        """
        记录失败信息
        
        Args:
            chunk_idx: 块索引
            original: 原始内容
            generated: 生成内容
            reason: 失败原因
            attempt_count: 尝试次数
        """
        failure_record = FailureRecord(
            chunk_idx=chunk_idx,
            original_content=original[:500] + "..." if len(original) > 500 else original,
            generated_content=generated[:500] + "..." if len(generated) > 500 else generated,
            failure_reason=reason,
            timestamp=datetime.now().isoformat(),
            attempt_count=attempt_count
        )
        
        self.failure_records.append(failure_record)
        
        # 写入日志文件
        log_file = self.log_dir / f"content_validation_failures_{datetime.now().strftime('%Y%m%d')}.log"
        
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                log_entry = {
                    'timestamp': failure_record.timestamp,
                    'chunk_idx': chunk_idx,
                    'reason': reason,
                    'attempt_count': attempt_count,
                    'original_length': len(original),
                    'generated_length': len(generated),
                    'validation_ratio': len(generated) / len(original) if len(original) > 0 else 0
                }
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logging.error(f"写入失败日志失败: {e}")
    
    def prompt_user_retry(self, chunk_idx: int) -> bool:
        """
        提示用户是否手动重试特定块
        
        Args:
            chunk_idx: 块索引
            
        Returns:
            用户是否选择重试
        """
        try:
            print(f"\n⚠️  块 {chunk_idx} 处理失败")
            print("选项:")
            print("  1. 跳过此块，继续处理其他块")
            print("  2. 手动重试此块")
            print("  3. 停止处理")
            
            while True:
                choice = input("请选择 (1/2/3): ").strip()
                
                if choice == '1':
                    return False
                elif choice == '2':
                    return True
                elif choice == '3':
                    raise KeyboardInterrupt("用户选择停止处理")
                else:
                    print("无效选择，请输入 1、2 或 3")
                    
        except KeyboardInterrupt:
            raise
        except Exception as e:
            logging.warning(f"用户交互失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, any]:
        """
        获取验证统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.stats.copy()
        stats['failure_count'] = len(self.failure_records)
        stats['success_rate'] = (
            stats['passed_validations'] / stats['total_validations'] 
            if stats['total_validations'] > 0 else 0
        )
        stats['retry_success_rate'] = (
            stats['successful_retries'] / stats['total_retries']
            if stats['total_retries'] > 0 else 0
        )
        
        return stats
    
    def save_failure_report(self, output_file: str = None) -> str:
        """
        保存失败报告
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            报告文件路径
        """
        if output_file is None:
            output_file = self.log_dir / f"validation_failure_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'stats': self.get_stats(),
            'failures': [
                {
                    'chunk_idx': record.chunk_idx,
                    'failure_reason': record.failure_reason,
                    'timestamp': record.timestamp,
                    'attempt_count': record.attempt_count,
                    'original_length': len(record.original_content),
                    'generated_length': len(record.generated_content)
                }
                for record in self.failure_records
            ]
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logging.info(f"失败报告已保存: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logging.error(f"保存失败报告失败: {e}")
            return ""


def main():
    """测试内容验证器功能"""
    validator = ContentValidator(threshold=0.8, max_retries=2)
    
    # 测试用例
    test_cases = [
        ("这是一个很长的测试文本，包含了很多内容。", "这是一个短文本。"),  # 应该失败
        ("短文本", "这是一个更长的生成文本，包含了更多内容。"),  # 应该通过
        ("测试内容", ""),  # 应该失败
        ("原始内容", '{"front": "问题", "back": "答案"}'),  # 应该通过
    ]
    
    for i, (original, generated) in enumerate(test_cases):
        print(f"\n测试用例 {i + 1}:")
        print(f"原始: {original}")
        print(f"生成: {generated}")
        
        result = validator.validate_content_quality(original, generated)
        print(f"验证结果: {'✅ 通过' if result.is_valid else '❌ 失败'}")
        print(f"原因: {result.reason}")
        
        if not result.is_valid:
            should_retry = validator.should_retry(0, result)
            print(f"是否重试: {'是' if should_retry else '否'}")
    
    # 显示统计信息
    print(f"\n统计信息: {validator.get_stats()}")


if __name__ == "__main__":
    main()
