#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_enhancements.py

测试四项增强功能的脚本
"""

import sys
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)

def test_html_optimizer():
    """测试HTML span优化器"""
    print("\n" + "="*60)
    print("🧪 测试 HTML Span 优化器")
    print("="*60)
    
    try:
        from html_optimizer import HtmlSpanOptimizer
        
        # 测试用例
        test_html = '''
        <p>这是一段测试文本。</p>
        <span style="color:rgb(51, 51, 51)">文本1</span> <span style="color:rgb(51, 51, 51)">文本2</span>
        <span style="color:rgb(255, 0, 0)">红色文本</span>
        <span style="color:rgb(51, 51, 51)">
        文本3</span><span style="color:rgb(51, 51, 51)">文本4</span>
        <p>结束文本。</p>
        '''
        
        print("原始HTML:")
        print(test_html)
        
        optimizer = HtmlSpanOptimizer()
        optimized_html = optimizer.optimize_spans(test_html)
        
        print("\n优化后HTML:")
        print(optimized_html)
        
        stats = optimizer.get_optimization_stats(test_html, optimized_html)
        print(f"\n优化统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("✅ HTML优化器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ HTML优化器测试失败: {e}")
        return False

def test_content_validator():
    """测试内容验证器"""
    print("\n" + "="*60)
    print("🧪 测试内容验证器")
    print("="*60)
    
    try:
        from content_validator import ContentValidator
        
        validator = ContentValidator(threshold=0.8, max_retries=2)
        
        # 测试用例
        test_cases = [
            ("这是一个很长的测试文本，包含了很多内容。", "这是一个短文本。", "应该失败"),
            ("短文本", "这是一个更长的生成文本，包含了更多内容。", "应该通过"),
            ("测试内容", "", "应该失败"),
            ("原始内容", '{"front": "问题", "back": "答案"}', "应该通过"),
        ]
        
        for i, (original, generated, expected) in enumerate(test_cases):
            print(f"\n测试用例 {i + 1} ({expected}):")
            print(f"原始: {original}")
            print(f"生成: {generated}")
            
            result = validator.validate_content_quality(original, generated)
            status = "✅ 通过" if result.is_valid else "❌ 失败"
            print(f"验证结果: {status}")
            print(f"原因: {result.reason}")
            
            if not result.is_valid:
                should_retry = validator.should_retry(0, result)
                print(f"是否重试: {'是' if should_retry else '否'}")
        
        # 显示统计信息
        stats = validator.get_stats()
        print(f"\n统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("✅ 内容验证器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 内容验证器测试失败: {e}")
        return False

def test_retry_manager():
    """测试重试管理器"""
    print("\n" + "="*60)
    print("🧪 测试重试管理器")
    print("="*60)
    
    try:
        from retry_manager import RetryManager
        
        retry_manager = RetryManager(max_retries=3, base_delay=0.1)
        
        # 测试成功的操作
        def success_func():
            return "成功结果"
        
        success, result = retry_manager.execute_with_retry(success_func, operation_id="test_success")
        print(f"成功操作: {success}, 结果: {result}")
        
        # 测试失败的操作
        def fail_func():
            raise Exception("模拟失败")
        
        success, result = retry_manager.execute_with_retry(fail_func, operation_id="test_fail")
        print(f"失败操作: {success}, 结果: {result}")
        
        # 测试需要重试的操作
        attempt_count = 0
        def retry_func():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                return "需要重试"
            return "最终成功"
        
        def retry_condition(result):
            return result == "需要重试"
        
        success, result = retry_manager.execute_with_retry(
            retry_func, 
            operation_id="test_retry",
            retry_condition=retry_condition
        )
        print(f"重试操作: {success}, 结果: {result}")
        
        # 显示统计信息
        stats = retry_manager.get_retry_statistics()
        print(f"\n统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("✅ 重试管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 重试管理器测试失败: {e}")
        return False

def test_smart_chunker():
    """测试智能分块器"""
    print("\n" + "="*60)
    print("🧪 测试智能分块器")
    print("="*60)
    
    try:
        from smart_chunker import SmartChunker
        
        # 测试文本
        test_text = """
        这是第一个问答对的问题部分。这里包含了一些详细的内容。
        
        {
            "front": "什么是人工智能？",
            "back": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"
        },
        
        这是第二个问答对的问题部分。内容继续展开。
        
        {
            "front": "机器学习的定义是什么？",
            "back": "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"
        },
        
        这里是更多的内容，用于测试分块功能。
        """ * 5  # 重复以创建足够长的文本
        
        chunker = SmartChunker(target_size=1000, min_size=500, max_size=1500)
        
        print("开始智能分块测试...")
        chunks = chunker.create_smart_chunks(test_text, enable_ai_analysis=False)
        
        print(f"\n分块结果:")
        print(f"总共创建了 {len(chunks)} 个分块")
        
        for i, chunk in enumerate(chunks):
            print(f"\n分块 {i}:")
            print(f"  大小: {len(chunk.content)} 字符")
            print(f"  位置: {chunk.start_pos} - {chunk.end_pos}")
            print(f"  内容预览: {chunk.content[:100]}...")
        
        stats = chunker.get_stats()
        print(f"\n统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("✅ 智能分块器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 智能分块器测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n" + "="*60)
    print("🧪 测试配置加载")
    print("="*60)
    
    try:
        from multi_channel_config import MultiChannelConfigParser
        
        # 测试配置解析
        config_parser = MultiChannelConfigParser("config.sample.ini")
        config = config_parser.get_config()
        
        print("配置加载成功:")
        print(f"  content_validation_threshold: {config.get('content_validation_threshold', 'Not found')}")
        print(f"  max_content_retries: {config.get('max_content_retries', 'Not found')}")
        print(f"  enable_smart_chunking: {config.get('enable_smart_chunking', 'Not found')}")
        print(f"  enable_html_optimization: {config.get('enable_html_optimization', 'Not found')}")
        
        print("✅ 配置加载测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def test_document_processor_integration():
    """测试文档处理器集成"""
    print("\n" + "="*60)
    print("🧪 测试文档处理器集成")
    print("="*60)
    
    try:
        from document_processor import DocumentProcessor
        
        # 创建测试用的markdown内容
        test_markdown = """# 测试文档

这是一个测试文档，用于验证智能分块功能。

## 第一部分

这里是第一部分的内容，包含了一些基础信息。

{
    "front": "什么是测试？",
    "back": "测试是验证系统功能是否正常的过程。"
},

## 第二部分

这里是第二部分的内容，继续展开更多信息。

{
    "front": "为什么要进行测试？",
    "back": "测试可以确保软件质量，发现和修复潜在问题。"
},

## 结论

测试是软件开发中不可或缺的环节。
"""
        
        # 创建临时markdown文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(test_markdown)
            temp_file_path = temp_file.name
        
        try:
            processor = DocumentProcessor()
            
            # 测试分块功能
            lines = test_markdown.split('\n')
            chunks = processor._create_chunks(lines)
            
            print(f"分块测试结果:")
            print(f"  生成了 {len(chunks)} 个分块")
            
            for i, (idx, content) in enumerate(chunks):
                print(f"  分块 {idx}: {len(content)} 字符")
            
            print("✅ 文档处理器集成测试通过")
            return True
            
        finally:
            # 清理临时文件
            Path(temp_file_path).unlink(missing_ok=True)
        
    except Exception as e:
        print(f"❌ 文档处理器集成测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始测试四项增强功能")
    print("="*80)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("HTML优化器", test_html_optimizer()))
    test_results.append(("内容验证器", test_content_validator()))
    test_results.append(("重试管理器", test_retry_manager()))
    test_results.append(("智能分块器", test_smart_chunker()))
    test_results.append(("配置加载", test_config_loading()))
    test_results.append(("文档处理器集成", test_document_processor_integration()))
    
    # 显示测试结果摘要
    print("\n" + "="*80)
    print("📊 测试结果摘要")
    print("="*80)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 所有测试都通过了！四项增强功能工作正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
