#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
smart_chunker.py

AI驱动的智能内容分块器
提供基于语义边界的智能分块功能，替代固定字符数分块
"""

import logging
import re
import json
from typing import List, Tuple, Dict, Optional, Any
from dataclasses import dataclass
from pathlib import Path


@dataclass
class ChunkBoundary:
    """分块边界信息"""
    position: int
    confidence: float
    reason: str
    context_before: str
    context_after: str


@dataclass
class SmartChunk:
    """智能分块结果"""
    index: int
    content: str
    start_pos: int
    end_pos: int
    boundary_info: Optional[ChunkBoundary] = None


class SmartChunker:
    """AI驱动的智能分块器"""
    
    def __init__(self, ai_service=None, target_size: int = 12000, 
                 min_size: int = 8000, max_size: int = 16000):
        """
        初始化智能分块器
        
        Args:
            ai_service: AI服务实例
            target_size: 目标分块大小（字符数）
            min_size: 最小分块大小
            max_size: 最大分块大小
        """
        self.ai_service = ai_service
        self.target_size = target_size
        self.min_size = min_size
        self.max_size = max_size
        
        # 分块统计
        self.stats = {
            'total_chunks': 0,
            'ai_analyzed_boundaries': 0,
            'fallback_chunks': 0,
            'average_chunk_size': 0
        }
    
    def create_smart_chunks(self, text: str, enable_ai_analysis: bool = True) -> List[SmartChunk]:
        """
        创建智能分块
        
        Args:
            text: 要分块的文本
            enable_ai_analysis: 是否启用AI分析
            
        Returns:
            智能分块列表
        """
        if not text or len(text) < self.min_size:
            # 文本太短，直接返回单个块
            return [SmartChunk(
                index=0,
                content=text,
                start_pos=0,
                end_pos=len(text)
            )]
        
        chunks = []
        current_pos = 0
        chunk_index = 0
        
        while current_pos < len(text):
            # 计算当前块的理想结束位置
            ideal_end = current_pos + self.target_size
            
            if ideal_end >= len(text):
                # 剩余文本不足一个完整块
                remaining_text = text[current_pos:]
                chunks.append(SmartChunk(
                    index=chunk_index,
                    content=remaining_text,
                    start_pos=current_pos,
                    end_pos=len(text)
                ))
                break
            
            # 寻找最佳分块边界
            boundary_pos = self._find_optimal_boundary(
                text, current_pos, ideal_end, enable_ai_analysis
            )
            
            # 创建分块
            chunk_content = text[current_pos:boundary_pos]
            chunks.append(SmartChunk(
                index=chunk_index,
                content=chunk_content,
                start_pos=current_pos,
                end_pos=boundary_pos
            ))
            
            current_pos = boundary_pos
            chunk_index += 1
        
        # 更新统计信息
        self._update_stats(chunks)
        
        return chunks
    
    def _find_optimal_boundary(self, text: str, start_pos: int, ideal_end: int, 
                              enable_ai_analysis: bool) -> int:
        """
        寻找最佳分块边界
        
        Args:
            text: 完整文本
            start_pos: 当前块开始位置
            ideal_end: 理想结束位置
            enable_ai_analysis: 是否启用AI分析
            
        Returns:
            最佳边界位置
        """
        # 定义搜索范围
        min_end = max(start_pos + self.min_size, ideal_end - 2000)
        max_end = min(len(text), ideal_end + 2000)
        
        # 如果启用AI分析且有AI服务
        if enable_ai_analysis and self.ai_service:
            ai_boundary = self._analyze_boundary_with_ai(text, start_pos, min_end, max_end)
            if ai_boundary:
                self.stats['ai_analyzed_boundaries'] += 1
                return ai_boundary
        
        # 降级到规则基础的边界检测
        self.stats['fallback_chunks'] += 1
        return self._find_rule_based_boundary(text, min_end, max_end)
    
    def _analyze_boundary_with_ai(self, text: str, start_pos: int, 
                                 min_end: int, max_end: int) -> Optional[int]:
        """
        使用AI分析最佳分块边界
        
        Args:
            text: 完整文本
            start_pos: 块开始位置
            min_end: 最小结束位置
            max_end: 最大结束位置
            
        Returns:
            AI建议的边界位置，如果失败则返回None
        """
        try:
            # 提取边界分析区域（前后各500字符的上下文）
            context_start = max(0, min_end - 500)
            context_end = min(len(text), max_end + 500)
            analysis_text = text[context_start:context_end]
            
            # 构建AI提示词
            prompt = self._build_boundary_analysis_prompt(
                analysis_text, 
                min_end - context_start,  # 相对位置
                max_end - context_start   # 相对位置
            )
            
            # 调用AI服务
            response = self._call_ai_for_boundary_analysis(prompt)
            
            if response:
                # 解析AI响应
                boundary_info = self._parse_boundary_response(response)
                if boundary_info and boundary_info.position:
                    # 转换回绝对位置
                    absolute_pos = context_start + boundary_info.position
                    if min_end <= absolute_pos <= max_end:
                        logging.debug(f"AI建议边界位置: {absolute_pos}, 原因: {boundary_info.reason}")
                        return absolute_pos
            
        except Exception as e:
            logging.warning(f"AI边界分析失败: {e}")
        
        return None
    
    def _build_boundary_analysis_prompt(self, text: str, min_pos: int, max_pos: int) -> str:
        """构建边界分析提示词"""
        return f"""请分析以下文本，找到最佳的分块边界位置。

文本内容：
{text}

要求：
1. 边界位置必须在字符位置 {min_pos} 到 {max_pos} 之间
2. 优先选择完整问答对的结束位置
3. 避免在句子中间分割
4. 保持上下文的连贯性

请返回JSON格式的响应：
{{
    "boundary_position": <位置>,
    "confidence": <0-1之间的置信度>,
    "reason": "<选择此位置的原因>",
    "context_before": "<边界前的上下文>",
    "context_after": "<边界后的上下文>"
}}"""
    
    def _call_ai_for_boundary_analysis(self, prompt: str) -> Optional[str]:
        """调用AI进行边界分析"""
        try:
            # 这里需要根据实际的AI服务接口进行调用
            # 由于AI服务的接口比较复杂，这里提供一个简化的调用方式
            
            # 构建简单的请求
            if hasattr(self.ai_service, '_call_single_channel_for_content'):
                # 选择一个通道
                channel = self.ai_service.select_channel()
                if channel:
                    response = self.ai_service._call_single_channel_for_content(
                        prompt, -1, channel  # 使用-1作为特殊索引表示边界分析
                    )
                    return response
            
        except Exception as e:
            logging.warning(f"AI边界分析调用失败: {e}")
        
        return None
    
    def _parse_boundary_response(self, response: str) -> Optional[ChunkBoundary]:
        """解析AI边界分析响应"""
        try:
            # 尝试解析JSON响应
            if response.strip().startswith('{'):
                data = json.loads(response)
                
                return ChunkBoundary(
                    position=data.get('boundary_position', 0),
                    confidence=data.get('confidence', 0.0),
                    reason=data.get('reason', ''),
                    context_before=data.get('context_before', ''),
                    context_after=data.get('context_after', '')
                )
            
        except (json.JSONDecodeError, KeyError) as e:
            logging.warning(f"解析AI边界响应失败: {e}")
        
        return None
    
    def _find_rule_based_boundary(self, text: str, min_end: int, max_end: int) -> int:
        """
        基于规则的边界检测（降级方案）
        
        Args:
            text: 文本
            min_end: 最小结束位置
            max_end: 最大结束位置
            
        Returns:
            边界位置
        """
        # 优先级顺序的边界模式
        boundary_patterns = [
            # 1. 问答对结束（最高优先级）
            (r'\n\s*\}\s*,?\s*\n', 1.0),
            # 2. 段落结束
            (r'\n\s*\n', 0.8),
            # 3. 句子结束
            (r'[。！？]\s*\n', 0.6),
            # 4. 逗号或分号后的换行
            (r'[，；]\s*\n', 0.4),
            # 5. 任何换行
            (r'\n', 0.2),
        ]
        
        best_pos = max_end
        best_score = 0.0
        
        for pattern, weight in boundary_patterns:
            matches = list(re.finditer(pattern, text[min_end:max_end]))
            
            for match in matches:
                pos = min_end + match.end()
                
                # 计算位置得分（越接近理想位置得分越高）
                ideal_pos = (min_end + max_end) // 2
                distance_score = 1.0 - abs(pos - ideal_pos) / (max_end - min_end)
                
                # 综合得分
                total_score = weight * distance_score
                
                if total_score > best_score:
                    best_score = total_score
                    best_pos = pos
        
        return best_pos
    
    def _update_stats(self, chunks: List[SmartChunk]):
        """更新分块统计信息"""
        self.stats['total_chunks'] = len(chunks)
        
        if chunks:
            total_size = sum(len(chunk.content) for chunk in chunks)
            self.stats['average_chunk_size'] = total_size // len(chunks)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取分块统计信息"""
        return self.stats.copy()
    
    def fallback_to_character_chunking(self, text: str, chunk_size: int, 
                                     chunk_stride: int) -> List[Tuple[int, str]]:
        """
        降级到字符基础分块（与现有接口兼容）
        
        Args:
            text: 文本
            chunk_size: 块大小
            chunk_stride: 步长
            
        Returns:
            (索引, 内容) 元组列表
        """
        chunks = []
        start = 0
        chunk_idx = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            chunk_content = text[start:end]
            
            chunks.append((chunk_idx, chunk_content))
            
            if end >= len(text):
                break
                
            start += chunk_stride
            chunk_idx += 1
        
        return chunks


def main():
    """测试智能分块器功能"""
    # 测试文本
    test_text = """
    这是第一个问答对的问题部分。这里包含了一些详细的内容。
    
    {
        "front": "什么是人工智能？",
        "back": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"
    },
    
    这是第二个问答对的问题部分。内容继续展开。
    
    {
        "front": "机器学习的定义是什么？",
        "back": "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"
    },
    
    这里是更多的内容，用于测试分块功能。
    """ * 10  # 重复以创建足够长的文本
    
    chunker = SmartChunker(target_size=1000, min_size=500, max_size=1500)
    
    print("开始智能分块测试...")
    chunks = chunker.create_smart_chunks(test_text, enable_ai_analysis=False)
    
    print(f"\n分块结果:")
    print(f"总共创建了 {len(chunks)} 个分块")
    
    for i, chunk in enumerate(chunks):
        print(f"\n分块 {i}:")
        print(f"  大小: {len(chunk.content)} 字符")
        print(f"  位置: {chunk.start_pos} - {chunk.end_pos}")
        print(f"  内容预览: {chunk.content[:100]}...")
    
    print(f"\n统计信息: {chunker.get_stats()}")


if __name__ == "__main__":
    main()
