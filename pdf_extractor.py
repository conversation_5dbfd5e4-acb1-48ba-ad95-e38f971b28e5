#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pdf_extractor.py

PDF文档内容提取器，将PDF文档转换为Markdown格式
支持文本和图片的提取，使用统一的文件管理系统
"""

import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional
import fitz  # PyMuPDF

from file_manager import FileManager
from content_filter import ContentFilter


class PDFExtractor:
    """PDF文档内容提取器"""
    
    def __init__(self, file_manager: FileManager):
        """
        初始化PDF提取器
        
        Args:
            file_manager: 文件管理器实例
        """
        self.file_manager = file_manager
        # 初始化内容过滤器
        try:
            self.content_filter = ContentFilter(trash_image_dir="trash_image")
            logging.info("内容过滤器初始化成功")
        except Exception as e:
            logging.warning(f"内容过滤器初始化失败，将跳过过滤功能: {e}")
            self.content_filter = None
    
    def extract_from_pdf(self, pdf_path: str, 
                        top_margin: float = 50, 
                        bottom_margin: float = 50) -> Dict[str, str]:
        """
        从PDF文档提取内容到Markdown
        
        Args:
            pdf_path: PDF文档路径
            top_margin: 顶部边距，忽略页眉
            bottom_margin: 底部边距，忽略页脚
            
        Returns:
            包含提取结果的字典
        """
        if not Path(pdf_path).exists():
            raise FileNotFoundError(f"PDF文档不存在: {pdf_path}")
        
        # 获取文件路径信息
        paths = self.file_manager.get_extraction_paths(pdf_path)
        project_name = paths['project_name']
        
        # 设置日志
        self._setup_project_logging(paths['log_file'])
        
        logging.info(f"开始提取PDF文档: {pdf_path}")
        logging.info(f"项目名称: {project_name}")
        logging.info(f"页面边距: 顶部={top_margin}, 底部={bottom_margin}")
        
        try:
            # 打开PDF文档
            doc = fitz.open(pdf_path)
            
            # 统计信息
            total_pages = len(doc)
            total_images = 0
            total_text_length = 0
            
            with open(paths['markdown_file'], "w", encoding="utf-8") as md_file:
                # 写入文档头部信息
                md_file.write(f"# {paths['project_name']}\n\n")
                md_file.write(f"提取自: {Path(pdf_path).name}\n")
                md_file.write(f"总页数: {total_pages}\n")
                md_file.write(f"提取时间: {self.file_manager.get_file_info(pdf_path)['timestamp']}\n\n")
                md_file.write("---\n\n")
                
                # 逐页处理
                for page_num in range(total_pages):
                    page = doc[page_num]                    # 写入页面标题
                    md_file.write(f"## 第 {page_num + 1} 页\n\n")
                    
                    # 使用新的智能提取方法
                    page_content = self._extract_page_with_smart_layout(
                        page, paths['images_dir'], project_name, page_num,
                        top_margin, bottom_margin, pdf_path
                    )
                    
                    if page_content['content'].strip():
                        md_file.write(page_content['content'])
                        md_file.write("\n\n")
                        total_text_length += page_content['text_length']
                        total_images += page_content['image_count']
                    
                    # 记录进度
                    if (page_num + 1) % 10 == 0:
                        logging.info(f"已处理 {page_num + 1}/{total_pages} 页")
            
            # 关闭文档
            doc.close()
            
            result = {
                'project_name': project_name,
                'markdown_file': str(paths['markdown_file']),
                'images_dir': str(paths['images_dir']),
                'total_pages': total_pages,
                'image_count': total_images,
                'text_length': total_text_length,
                'cache_dir': str(paths['cache_dir']),
                'anki_file': str(paths['anki_file'])
            }
            
            logging.info(f"PDF文档提取完成:")
            logging.info(f"  - Markdown文件: {paths['markdown_file']}")
            logging.info(f"  - 总页数: {total_pages}")
            logging.info(f"  - 图片数: {total_images}")
            logging.info(f"  - 文本长度: {total_text_length} 字符")
            
            return result            
        except Exception as e:
            logging.error(f"提取PDF文档失败: {e}")
            raise e
    
    def _extract_page_content_with_positions(self, page, top_margin: float, bottom_margin: float) -> Dict:
        """
        从页面提取内容，包含位置信息
        
        Args:
            page: PDF页面对象
            top_margin: 顶部边距
            bottom_margin: 底部边距
            
        Returns:
            包含文本块和图片位置信息的字典
        """
        text_blocks = []
        image_blocks = []
        
        try:
            # 获取页面的文本块
            blocks = page.get_text("dict")["blocks"]
            
            # 计算实际的底部边距
            actual_bottom_margin = page.rect.height - bottom_margin
            
            # 处理文本块
            for block in blocks:
                if block["type"] == 0:  # 文本块
                    bbox = block["bbox"]  # (x0, y0, x1, y1)
                    
                    # 检查是否在边距范围内
                    if bbox[1] < top_margin or bbox[3] > actual_bottom_margin:
                        continue
                      # 提取文本内容
                    block_text = []
                    for line in block["lines"]:
                        line_text = ""
                        for span in line["spans"]:
                            line_text += span["text"]
                        
                        if line_text.strip():
                            block_text.append(line_text.strip())
                    
                    if block_text:
                        text_content = '\n'.join(block_text)
                        text_blocks.append({
                            'text': text_content,
                            'content': text_content,  # 为内容过滤器提供的字段
                            'bbox': bbox,  # (x0, y0, x1, y1)
                            'y_center': (bbox[1] + bbox[3]) / 2,
                            'x_center': (bbox[0] + bbox[2]) / 2
                        })
            
            # 获取图片信息
            image_list = page.get_images(full=True)
            for img_index, img in enumerate(image_list):
                try:
                    # 获取图片在页面上的位置
                    img_rects = page.get_image_rects(img[0])
                    if img_rects:
                        # 使用第一个找到的矩形
                        rect = img_rects[0]
                        bbox = (rect.x0, rect.y0, rect.x1, rect.y1)                        # 检查是否在边距范围内
                        if bbox[1] >= top_margin and bbox[3] <= actual_bottom_margin:
                            # 获取图片的二进制数据用于过滤
                            try:
                                xref = img[0]
                                pix = fitz.Pixmap(page.parent, xref)
                                if pix.n - pix.alpha < 4:  # 确保不是CMYK
                                    img_data = pix.tobytes("png")
                                else:
                                    pix_rgb = fitz.Pixmap(fitz.csRGB, pix)
                                    img_data = pix_rgb.tobytes("png")
                                    pix_rgb = None
                                pix = None
                            except Exception as e:
                                logging.warning(f"获取图片数据失败: {e}")
                                img_data = None
                            
                            image_blocks.append({
                                'img_index': img_index,
                                'img_data': img,
                                'data': img_data,  # 为内容过滤器提供的二进制数据
                                'filename': f'img_{img_index}',  # 临时文件名
                                'bbox': bbox,
                                'y_center': (bbox[1] + bbox[3]) / 2,
                                'x_center': (bbox[0] + bbox[2]) / 2
                            })
                except Exception as e:
                    logging.warning(f"获取图片 {img_index} 位置信息失败: {e}")
            
        except Exception as e:
            logging.warning(f"页面内容位置提取失败: {e}")
        
        return {
            'text_blocks': text_blocks,
            'image_blocks': image_blocks,
            'images': image_blocks  # 为内容过滤器提供的字段
        }
    
    def _analyze_image_text_relationships(self, text_blocks: List[Dict], image_blocks: List[Dict]) -> List[Dict]:
        """
        分析图片与文本的位置关系
        
        Args:
            text_blocks: 文本块列表
            image_blocks: 图片块列表
            
        Returns:
            带有位置关系的图片信息列表
        """
        image_relationships = []
        
        for img_block in image_blocks:
            img_bbox = img_block['bbox']
            img_y_center = img_block['y_center']
            
            # 寻找与图片最相关的文本块
            closest_above = None
            closest_below = None
            closest_above_distance = float('inf')
            closest_below_distance = float('inf')
            
            overlapping_text = []
            
            for i, text_block in enumerate(text_blocks):
                text_bbox = text_block['bbox']
                text_y_center = text_block['y_center']
                
                # 检查水平重叠 (X轴重叠)
                x_overlap = not (img_bbox[2] < text_bbox[0] or img_bbox[0] > text_bbox[2])
                
                if x_overlap:
                    # 文本在图片上方
                    if text_bbox[3] <= img_bbox[1]:
                        distance = img_bbox[1] - text_bbox[3]
                        if distance < closest_above_distance:
                            closest_above_distance = distance
                            closest_above = i
                    
                    # 文本在图片下方
                    elif text_bbox[1] >= img_bbox[3]:
                        distance = text_bbox[1] - img_bbox[3]
                        if distance < closest_below_distance:
                            closest_below_distance = distance
                            closest_below = i
                    
                    # 文本与图片垂直重叠
                    else:
                        overlapping_text.append(i)
            
            # 确定图片插入位置
            position_info = {
                'img_block': img_block,
                'position_type': 'standalone',  # 默认独立位置
                'insert_before_text': None,
                'insert_after_text': None,
                'closest_above': closest_above,
                'closest_below': closest_below,
                'overlapping_text': overlapping_text
            }
            
            # 判断最佳插入位置
            if closest_above is not None and closest_below is not None:
                # 图片在两段文本之间
                if closest_above_distance < 50 and closest_below_distance < 50:
                    position_info['position_type'] = 'between_paragraphs'
                    position_info['insert_after_text'] = closest_above
                elif closest_above_distance < closest_below_distance:
                    position_info['position_type'] = 'after_paragraph'
                    position_info['insert_after_text'] = closest_above
                else:
                    position_info['position_type'] = 'before_paragraph'
                    position_info['insert_before_text'] = closest_below
            elif closest_above is not None:
                position_info['position_type'] = 'after_paragraph'
                position_info['insert_after_text'] = closest_above
            elif closest_below is not None:
                position_info['position_type'] = 'before_paragraph'
                position_info['insert_before_text'] = closest_below
            elif overlapping_text:
                # 图片与文本重叠，可能是内嵌图片
                position_info['position_type'] = 'inline'
                position_info['insert_after_text'] = overlapping_text[0]
            
            image_relationships.append(position_info)
        
        return image_relationships
    
    def _extract_page_text(self, page, top_margin: float, bottom_margin: float) -> str:
        """
        从页面提取文本内容（兼容性方法）
        
        Args:
            page: PDF页面对象
            top_margin: 顶部边距
            bottom_margin: 底部边距
            
        Returns:
            页面文本内容
        """
        content = self._extract_page_content_with_positions(page, top_margin, bottom_margin)
        text_content = []
        
        for text_block in content['text_blocks']:
            text_content.append(text_block['text'])
        
        return "\n\n".join(text_content)
    
    def _extract_page_images(self, page, images_dir: Path, 
                           project_name: str, page_num: int) -> List[str]:
        """
        从页面提取图片
        
        Args:
            page: PDF页面对象
            images_dir: 图片保存目录
            project_name: 项目名称
            page_num: 页码
            
        Returns:
            提取的图片文件名列表
        """
        extracted_images = []
        
        try:
            # 获取页面中的图片
            image_list = page.get_images(full=True)
            
            for img_index, img in enumerate(image_list):
                try:
                    # 获取图片数据
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)
                    
                    # 生成标准化的图片名称（使用简化命名）
                    img_name = self.file_manager.get_image_name(
                        project_name, page_num + 1, img_index + 1, 'page', use_simplified_naming=True
                    )
                    
                    img_path = images_dir / img_name
                    
                    # 保存图片
                    if pix.n < 5:  # 灰度或RGB
                        pix.save(str(img_path))
                    else:  # CMYK需要转换
                        pix_rgb = fitz.Pixmap(fitz.csRGB, pix)
                        pix_rgb.save(str(img_path))
                        pix_rgb = None
                    
                    pix = None
                    extracted_images.append(img_name)
                    
                    logging.debug(f"提取图片: {img_name}")
                    
                except Exception as e:
                    logging.warning(f"页面 {page_num + 1} 图片 {img_index + 1} 提取失败: {e}")
                    
        except Exception as e:
            logging.warning(f"页面 {page_num + 1} 图片批量提取失败: {e}")
        
        return extracted_images
    
    def _setup_project_logging(self, log_file: Path):
        """
        为项目设置独立的日志记录
        
        Args:
            log_file: 日志文件路径
        """
        # 创建项目专用的logger
        project_logger = logging.getLogger(f"pdf_extractor_{log_file.stem}")
        project_logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not project_logger.handlers:
            fmt = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
            
            # 文件处理器
            fh = logging.FileHandler(log_file, encoding="utf-8")
            fh.setLevel(logging.DEBUG)
            fh.setFormatter(fmt)
            project_logger.addHandler(fh)
    
    def _extract_page_with_smart_layout(self, page, images_dir: Path,
                                       project_name: str, page_num: int,
                                       top_margin: float, bottom_margin: float, pdf_path: str = None) -> Dict:
        """
        智能提取页面内容，根据位置关系合理排版
        
        Args:
            page: PDF页面对象
            images_dir: 图片保存目录
            project_name: 项目名称
            page_num: 页码
            top_margin: 顶部边距
            bottom_margin: 底部边距
            
        Returns:
            包含排版后内容的字典
        """        # 获取页面所有内容的位置信息
        content_info = self._extract_page_content_with_positions(page, top_margin, bottom_margin)
          # 应用内容过滤
        if self.content_filter:
            content_info = self.content_filter.filter_page_content(content_info, page_num)
        
        text_blocks = content_info['text_blocks']
        image_blocks = content_info['image_blocks']
        
        if not text_blocks and not image_blocks:
            return {'content': '', 'text_length': 0, 'image_count': 0}
        
        # 分析图片与文本的关系
        image_relationships = self._analyze_image_text_relationships(text_blocks, image_blocks)
        
        # 保存图片并生成引用
        saved_images = {}
        for img_rel in image_relationships:
            img_block = img_rel['img_block']
            try:
                img_name = self._save_single_image(
                    page, img_block['img_data'], images_dir, 
                    project_name, page_num, img_block['img_index']
                )
                saved_images[img_block['img_index']] = img_name
                logging.debug(f"保存图片: {img_name}, 位置类型: {img_rel['position_type']}")
            except Exception as e:
                logging.warning(f"保存图片失败: {e}")
        
        # 生成智能排版的内容
        content_parts = []
        image_inserted = set()  # 记录已插入的图片
        
        # 按垂直位置排序文本块
        sorted_text_blocks = sorted(text_blocks, key=lambda x: x['y_center'])
        
        for i, text_block in enumerate(sorted_text_blocks):
            # 添加文本内容
            content_parts.append(text_block['text'])
            
            # 检查是否需要在此文本后插入图片
            for img_rel in image_relationships:
                img_index = img_rel['img_block']['img_index']
                if img_index in image_inserted:
                    continue
                    
                should_insert = False
                
                # 根据位置关系决定是否插入
                if (img_rel['position_type'] == 'after_paragraph' and 
                    img_rel['insert_after_text'] == i):
                    should_insert = True
                elif (img_rel['position_type'] == 'between_paragraphs' and 
                      img_rel['insert_after_text'] == i):
                    should_insert = True
                elif (img_rel['position_type'] == 'inline' and 
                      img_rel['insert_after_text'] == i):
                    should_insert = True
                
                if should_insert and img_index in saved_images:
                    img_name = saved_images[img_index]
                    # 修复图片路径：使用images_documentname格式而不是images
                    if pdf_path:
                        from document_utils import DocumentNameCleaner
                        name_cleaner = DocumentNameCleaner()
                        clean_doc_name = name_cleaner.clean_document_name(Path(pdf_path).stem)
                        img_path = f"images_{clean_doc_name}/{img_name}"
                    else:
                        img_path = f"images/{img_name}"  # 向后兼容
                    content_parts.append(f"\n![{img_name}]({img_path})\n")
                    image_inserted.add(img_index)
        
        # 添加未插入的图片（通常是独立位置的图片）
        for img_rel in image_relationships:
            img_index = img_rel['img_block']['img_index']
            if img_index not in image_inserted and img_index in saved_images:
                img_name = saved_images[img_index]
                
                # 根据位置类型确定插入位置
                # 修复图片路径：使用images_documentname格式而不是images
                if pdf_path:
                    from document_utils import DocumentNameCleaner
                    name_cleaner = DocumentNameCleaner()
                    clean_doc_name = name_cleaner.clean_document_name(Path(pdf_path).stem)
                    img_path = f"images_{clean_doc_name}/{img_name}"
                else:
                    img_path = f"images/{img_name}"  # 向后兼容

                if img_rel['position_type'] == 'before_paragraph' and img_rel['insert_before_text'] is not None:
                    # 在指定文本前插入
                    insert_pos = img_rel['insert_before_text']
                    if insert_pos < len(content_parts):
                        content_parts.insert(insert_pos * 2, f"![{img_name}]({img_path})\n\n")
                else:
                    # 默认添加到末尾
                    content_parts.append(f"\n![{img_name}]({img_path})\n")
                
                image_inserted.add(img_index)
        
        # 合并内容
        final_content = '\n\n'.join(content_parts)
        
        return {
            'content': final_content,
            'text_length': sum(len(block['text']) for block in text_blocks),
            'image_count': len(saved_images)
        }
    
    def _save_single_image(self, page, img_data, images_dir: Path, 
                          project_name: str, page_num: int, img_index: int) -> str:
        """
        保存单个图片
        
        Args:
            page: PDF页面对象
            img_data: 图片数据
            images_dir: 图片目录
            project_name: 项目名称
            page_num: 页码
            img_index: 图片索引
            
        Returns:
            保存的图片文件名
        """
        # 获取图片数据
        xref = img_data[0]
        pix = fitz.Pixmap(page.parent, xref)
        
        # 生成标准化的图片名称（使用简化命名）
        img_name = self.file_manager.get_image_name(
            project_name, page_num + 1, img_index + 1, 'page', use_simplified_naming=True
        )
        
        img_path = images_dir / img_name
        
        # 保存图片
        if pix.n < 5:  # 灰度或RGB
            pix.save(str(img_path))
        else:  # CMYK需要转换
            pix_rgb = fitz.Pixmap(fitz.csRGB, pix)
            pix_rgb.save(str(img_path))
            pix_rgb = None
        
        pix = None
        return img_name

def extract_pdf_to_markdown(pdf_path: str, base_dir: str = "documents",
                          top_margin: float = 50, 
                          bottom_margin: float = 50) -> Dict[str, str]:
    """
    便捷函数：将PDF文档转换为Markdown
    
    Args:
        pdf_path: PDF文档路径
        base_dir: 基础工作目录
        top_margin: 顶部边距
        bottom_margin: 底部边距
        
    Returns:
        提取结果字典
    """
    file_manager = FileManager(base_dir)
    extractor = PDFExtractor(file_manager)
    return extractor.extract_from_pdf(pdf_path, top_margin, bottom_margin)


def main():
    """测试PDF提取功能"""
    if len(sys.argv) < 2:
        print("使用方法: python pdf_extractor.py <pdf_file_path> [base_dir] [top_margin] [bottom_margin]")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    base_dir = sys.argv[2] if len(sys.argv) > 2 else "documents"
    top_margin = float(sys.argv[3]) if len(sys.argv) > 3 else 50
    bottom_margin = float(sys.argv[4]) if len(sys.argv) > 4 else 50
    
    # 设置基础日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )
    
    try:
        result = extract_pdf_to_markdown(pdf_path, base_dir, top_margin, bottom_margin)
        print("\n提取完成!")
        print(f"项目名称: {result['project_name']}")
        print(f"Markdown文件: {result['markdown_file']}")
        print(f"总页数: {result['total_pages']}")
        print(f"图片数量: {result['image_count']}")
        print(f"文本长度: {result['text_length']} 字符")
        
    except Exception as e:
        print(f"提取失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
