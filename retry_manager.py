#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
retry_manager.py

重试管理器模块
提供智能重试逻辑、失败处理和统计功能
"""

import time
import logging
import random
from typing import Callable, Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path


@dataclass
class RetryAttempt:
    """重试尝试记录"""
    attempt_number: int
    timestamp: str
    success: bool
    error_message: str = ""
    response_time: float = 0.0


@dataclass
class RetryStats:
    """重试统计信息"""
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    total_retries: int = 0
    successful_retries: int = 0
    average_attempts: float = 0.0


class RetryManager:
    """重试管理器"""
    
    def __init__(self, max_retries: int = 2, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        """
        初始化重试管理器
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            backoff_factor: 退避因子
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        
        # 统计信息
        self.stats = RetryStats()
        
        # 操作记录
        self.operation_records: Dict[str, List[RetryAttempt]] = {}
        
        # 失败处理回调
        self.failure_handlers: List[Callable] = []
    
    def execute_with_retry(self, func: Callable, *args, operation_id: str = None, 
                          retry_condition: Callable[[Any], bool] = None, **kwargs) -> Tuple[bool, Any]:
        """
        执行带重试的操作
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            operation_id: 操作标识符
            retry_condition: 重试条件函数，返回True表示需要重试
            **kwargs: 函数关键字参数
            
        Returns:
            (是否成功, 结果)
        """
        if operation_id is None:
            operation_id = f"op_{int(time.time() * 1000)}"
        
        self.stats.total_operations += 1
        attempts = []
        
        for attempt in range(self.max_retries + 1):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                response_time = time.time() - start_time
                
                # 检查是否需要重试
                if retry_condition and retry_condition(result):
                    if attempt < self.max_retries:
                        # 记录失败的尝试
                        attempts.append(RetryAttempt(
                            attempt_number=attempt + 1,
                            timestamp=datetime.now().isoformat(),
                            success=False,
                            error_message="重试条件未满足",
                            response_time=response_time
                        ))
                        
                        # 等待后重试
                        delay = self._calculate_delay(attempt)
                        logging.debug(f"操作 {operation_id} 第 {attempt + 1} 次尝试失败，{delay:.2f}s 后重试")
                        time.sleep(delay)
                        self.stats.total_retries += 1
                        continue
                    else:
                        # 达到最大重试次数
                        attempts.append(RetryAttempt(
                            attempt_number=attempt + 1,
                            timestamp=datetime.now().isoformat(),
                            success=False,
                            error_message="达到最大重试次数",
                            response_time=response_time
                        ))
                        
                        self.operation_records[operation_id] = attempts
                        self.stats.failed_operations += 1
                        self._handle_failure(operation_id, "达到最大重试次数", attempts)
                        return False, None
                
                # 成功
                attempts.append(RetryAttempt(
                    attempt_number=attempt + 1,
                    timestamp=datetime.now().isoformat(),
                    success=True,
                    response_time=response_time
                ))
                
                self.operation_records[operation_id] = attempts
                self.stats.successful_operations += 1
                
                if attempt > 0:
                    self.stats.successful_retries += 1
                    logging.info(f"操作 {operation_id} 在第 {attempt + 1} 次尝试后成功")
                
                return True, result
                
            except Exception as e:
                response_time = time.time() - start_time
                error_message = str(e)
                
                attempts.append(RetryAttempt(
                    attempt_number=attempt + 1,
                    timestamp=datetime.now().isoformat(),
                    success=False,
                    error_message=error_message,
                    response_time=response_time
                ))
                
                if attempt < self.max_retries:
                    delay = self._calculate_delay(attempt)
                    logging.warning(f"操作 {operation_id} 第 {attempt + 1} 次尝试异常: {error_message}，{delay:.2f}s 后重试")
                    time.sleep(delay)
                    self.stats.total_retries += 1
                else:
                    # 达到最大重试次数
                    logging.error(f"操作 {operation_id} 在 {attempt + 1} 次尝试后仍然失败: {error_message}")
                    self.operation_records[operation_id] = attempts
                    self.stats.failed_operations += 1
                    self._handle_failure(operation_id, error_message, attempts)
                    return False, None
        
        return False, None
    
    def _calculate_delay(self, attempt: int) -> float:
        """
        计算延迟时间（指数退避 + 随机抖动）
        
        Args:
            attempt: 尝试次数（从0开始）
            
        Returns:
            延迟时间（秒）
        """
        # 指数退避
        delay = self.base_delay * (self.backoff_factor ** attempt)
        
        # 限制最大延迟
        delay = min(delay, self.max_delay)
        
        # 添加随机抖动（±25%）
        jitter = delay * 0.25 * (2 * random.random() - 1)
        delay += jitter
        
        return max(0.1, delay)  # 最小延迟0.1秒
    
    def add_failure_handler(self, handler: Callable[[str, str, List[RetryAttempt]], None]):
        """
        添加失败处理回调
        
        Args:
            handler: 失败处理函数，接收(operation_id, error_message, attempts)参数
        """
        self.failure_handlers.append(handler)
    
    def _handle_failure(self, operation_id: str, error_message: str, attempts: List[RetryAttempt]):
        """
        处理失败情况
        
        Args:
            operation_id: 操作标识符
            error_message: 错误信息
            attempts: 尝试记录列表
        """
        for handler in self.failure_handlers:
            try:
                handler(operation_id, error_message, attempts)
            except Exception as e:
                logging.error(f"失败处理回调执行失败: {e}")
    
    def get_retry_statistics(self) -> Dict[str, Any]:
        """
        获取重试统计信息
        
        Returns:
            统计信息字典
        """
        # 计算平均尝试次数
        if self.stats.total_operations > 0:
            total_attempts = sum(len(attempts) for attempts in self.operation_records.values())
            self.stats.average_attempts = total_attempts / self.stats.total_operations
        
        return {
            'total_operations': self.stats.total_operations,
            'successful_operations': self.stats.successful_operations,
            'failed_operations': self.stats.failed_operations,
            'success_rate': (
                self.stats.successful_operations / self.stats.total_operations 
                if self.stats.total_operations > 0 else 0
            ),
            'total_retries': self.stats.total_retries,
            'successful_retries': self.stats.successful_retries,
            'retry_success_rate': (
                self.stats.successful_retries / self.stats.total_retries
                if self.stats.total_retries > 0 else 0
            ),
            'average_attempts': self.stats.average_attempts,
            'failed_operation_ids': [
                op_id for op_id, attempts in self.operation_records.items()
                if not attempts[-1].success
            ]
        }
    
    def get_operation_details(self, operation_id: str) -> Optional[List[RetryAttempt]]:
        """
        获取特定操作的详细信息
        
        Args:
            operation_id: 操作标识符
            
        Returns:
            尝试记录列表
        """
        return self.operation_records.get(operation_id)
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = RetryStats()
        self.operation_records.clear()
    
    def save_failure_report(self, output_file: str) -> bool:
        """
        保存失败报告
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            是否成功保存
        """
        try:
            import json
            
            failed_operations = {
                op_id: [
                    {
                        'attempt_number': attempt.attempt_number,
                        'timestamp': attempt.timestamp,
                        'success': attempt.success,
                        'error_message': attempt.error_message,
                        'response_time': attempt.response_time
                    }
                    for attempt in attempts
                ]
                for op_id, attempts in self.operation_records.items()
                if not attempts[-1].success
            }
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'statistics': self.get_retry_statistics(),
                'failed_operations': failed_operations
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logging.info(f"重试失败报告已保存: {output_file}")
            return True
            
        except Exception as e:
            logging.error(f"保存重试失败报告失败: {e}")
            return False


def main():
    """测试重试管理器功能"""
    retry_manager = RetryManager(max_retries=3, base_delay=0.5)
    
    # 添加失败处理回调
    def failure_handler(op_id: str, error: str, attempts: List[RetryAttempt]):
        print(f"操作 {op_id} 最终失败: {error}")
        print(f"总共尝试了 {len(attempts)} 次")
    
    retry_manager.add_failure_handler(failure_handler)
    
    # 测试成功的操作
    def success_func():
        return "成功结果"
    
    success, result = retry_manager.execute_with_retry(success_func, operation_id="test_success")
    print(f"成功操作: {success}, 结果: {result}")
    
    # 测试失败的操作
    def fail_func():
        raise Exception("模拟失败")
    
    success, result = retry_manager.execute_with_retry(fail_func, operation_id="test_fail")
    print(f"失败操作: {success}, 结果: {result}")
    
    # 测试需要重试的操作
    attempt_count = 0
    def retry_func():
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            return "需要重试"
        return "最终成功"
    
    def retry_condition(result):
        return result == "需要重试"
    
    success, result = retry_manager.execute_with_retry(
        retry_func, 
        operation_id="test_retry",
        retry_condition=retry_condition
    )
    print(f"重试操作: {success}, 结果: {result}")
    
    # 显示统计信息
    stats = retry_manager.get_retry_statistics()
    print(f"\n统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
