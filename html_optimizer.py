#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
html_optimizer.py

HTML内容优化器模块
提供HTML span元素合并和优化功能
"""

import re
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass


@dataclass
class SpanElement:
    """HTML span元素数据类"""
    style: str
    content: str
    start_pos: int
    end_pos: int


class HtmlSpanOptimizer:
    """HTML span元素优化器"""
    
    def __init__(self):
        """初始化HTML span优化器"""
        self.span_pattern = re.compile(
            r'<span\s+style="([^"]*)"[^>]*>(.*?)</span>',
            re.DOTALL | re.IGNORECASE
        )
    
    def optimize_spans(self, html_content: str) -> str:
        """
        优化HTML内容中的span元素
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            优化后的HTML内容
        """
        if not html_content or '<span' not in html_content:
            return html_content
        
        try:
            # 提取所有span元素
            spans = self._extract_spans(html_content)
            
            if len(spans) < 2:
                return html_content
            
            # 合并连续的相同样式span元素
            merged_spans = self._merge_consecutive_spans(spans, html_content)
            
            # 重建HTML内容
            optimized_content = self._rebuild_html_content(html_content, merged_spans)
            
            logging.debug(f"HTML span优化完成: {len(spans)} -> {len(merged_spans)} 个span元素")
            
            return optimized_content
            
        except Exception as e:
            logging.warning(f"HTML span优化失败: {e}")
            return html_content
    
    def _extract_spans(self, html_content: str) -> List[SpanElement]:
        """
        提取HTML内容中的所有span元素
        
        Args:
            html_content: HTML内容
            
        Returns:
            span元素列表
        """
        spans = []
        
        for match in self.span_pattern.finditer(html_content):
            style = match.group(1).strip()
            content = match.group(2)
            start_pos = match.start()
            end_pos = match.end()
            
            spans.append(SpanElement(
                style=style,
                content=content,
                start_pos=start_pos,
                end_pos=end_pos
            ))
        
        return spans
    
    def _merge_consecutive_spans(self, spans: List[SpanElement], html_content: str) -> List[SpanElement]:
        """
        合并连续的相同样式span元素
        
        Args:
            spans: span元素列表
            html_content: 原始HTML内容
            
        Returns:
            合并后的span元素列表
        """
        if not spans:
            return spans
        
        merged_spans = []
        current_span = spans[0]
        
        for i in range(1, len(spans)):
            next_span = spans[i]
            
            # 检查是否可以合并
            if self._can_merge_spans(current_span, next_span, html_content):
                # 合并span元素
                between_content = html_content[current_span.end_pos:next_span.start_pos]
                current_span = SpanElement(
                    style=current_span.style,
                    content=current_span.content + between_content + next_span.content,
                    start_pos=current_span.start_pos,
                    end_pos=next_span.end_pos
                )
            else:
                # 不能合并，保存当前span并开始新的span
                merged_spans.append(current_span)
                current_span = next_span
        
        # 添加最后一个span
        merged_spans.append(current_span)
        
        return merged_spans
    
    def _can_merge_spans(self, span1: SpanElement, span2: SpanElement, html_content: str) -> bool:
        """
        检查两个span元素是否可以合并
        
        Args:
            span1: 第一个span元素
            span2: 第二个span元素
            html_content: 原始HTML内容
            
        Returns:
            是否可以合并
        """
        # 样式必须完全相同
        if span1.style != span2.style:
            return False
        
        # 检查两个span之间的内容
        between_content = html_content[span1.end_pos:span2.start_pos]
        
        # 只允许空白字符（空格、换行、制表符）
        if between_content and not re.match(r'^[\s]*$', between_content):
            return False
        
        # 检查两个span之间的距离，如果距离太远则不合并
        if span2.start_pos - span1.end_pos > 100:  # 最大允许100个字符的间隔
            return False
        
        return True
    
    def _rebuild_html_content(self, original_content: str, merged_spans: List[SpanElement]) -> str:
        """
        根据合并后的span元素重建HTML内容
        
        Args:
            original_content: 原始HTML内容
            merged_spans: 合并后的span元素列表
            
        Returns:
            重建后的HTML内容
        """
        if not merged_spans:
            return original_content
        
        # 按位置排序
        merged_spans.sort(key=lambda x: x.start_pos)
        
        result = []
        last_pos = 0
        
        for span in merged_spans:
            # 添加span之前的内容
            result.append(original_content[last_pos:span.start_pos])
            
            # 添加合并后的span
            result.append(f'<span style="{span.style}">{span.content}</span>')
            
            last_pos = span.end_pos
        
        # 添加最后一个span之后的内容
        result.append(original_content[last_pos:])
        
        return ''.join(result)
    
    def get_optimization_stats(self, original_content: str, optimized_content: str) -> Dict[str, int]:
        """
        获取优化统计信息
        
        Args:
            original_content: 原始内容
            optimized_content: 优化后内容
            
        Returns:
            优化统计信息
        """
        original_spans = len(self.span_pattern.findall(original_content))
        optimized_spans = len(self.span_pattern.findall(optimized_content))
        
        return {
            'original_spans': original_spans,
            'optimized_spans': optimized_spans,
            'spans_reduced': original_spans - optimized_spans,
            'reduction_percentage': round((original_spans - optimized_spans) / original_spans * 100, 2) if original_spans > 0 else 0,
            'original_length': len(original_content),
            'optimized_length': len(optimized_content),
            'size_reduction': len(original_content) - len(optimized_content)
        }


def optimize_html_spans(html_content: str) -> str:
    """
    便捷函数：优化HTML内容中的span元素
    
    Args:
        html_content: HTML内容
        
    Returns:
        优化后的HTML内容
    """
    optimizer = HtmlSpanOptimizer()
    return optimizer.optimize_spans(html_content)


def main():
    """测试HTML span优化功能"""
    # 测试用例
    test_html = '''
    <p>这是一段测试文本。</p>
    <span style="color:rgb(51, 51, 51)">文本1</span> <span style="color:rgb(51, 51, 51)">文本2</span>
    <span style="color:rgb(255, 0, 0)">红色文本</span>
    <span style="color:rgb(51, 51, 51)">
    文本3</span><span style="color:rgb(51, 51, 51)">文本4</span>
    <p>结束文本。</p>
    '''
    
    print("原始HTML:")
    print(test_html)
    print("\n" + "="*50 + "\n")
    
    optimizer = HtmlSpanOptimizer()
    optimized_html = optimizer.optimize_spans(test_html)
    
    print("优化后HTML:")
    print(optimized_html)
    print("\n" + "="*50 + "\n")
    
    stats = optimizer.get_optimization_stats(test_html, optimized_html)
    print("优化统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
