#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
quick_fix_validation.py

快速修复验证问题的脚本
临时禁用内容验证功能，让项目能够正常处理
"""

import sys
from pathlib import Path

def disable_validation_in_ai_service():
    """在ai_service.py中临时禁用验证功能"""
    ai_service_path = Path("ai_service.py")
    
    if not ai_service_path.exists():
        print("❌ ai_service.py 文件不存在")
        return False
    
    # 读取文件内容
    with open(ai_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    backup_path = ai_service_path.with_suffix('.py.backup')
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"✅ 已备份原文件到: {backup_path}")
    
    # 修改call_api方法，强制使用传统模式
    old_code = """    def call_api(self, chunk: str, idx: int, cache_dir: str = None) -> bool:
        \"\"\"调用 AI API 提取问答对，支持多通道、负载均衡、内容验证和智能重试

        Args:
            chunk: 文本块内容
            idx: 块索引
            cache_dir: 缓存目录，如果为None则不保存缓存

        Returns:
            bool: 是否成功处理
        \"\"\"
        # 如果启用了验证和重试机制
        if self.content_validator and self.retry_manager:
            return self._call_api_with_validation(chunk, idx, cache_dir)
        else:
            # 使用原有的逻辑（向后兼容）
            return self._call_api_legacy(chunk, idx, cache_dir)"""
    
    new_code = """    def call_api(self, chunk: str, idx: int, cache_dir: str = None) -> bool:
        \"\"\"调用 AI API 提取问答对，支持多通道、负载均衡、内容验证和智能重试

        Args:
            chunk: 文本块内容
            idx: 块索引
            cache_dir: 缓存目录，如果为None则不保存缓存

        Returns:
            bool: 是否成功处理
        \"\"\"
        # 临时禁用验证功能，直接使用传统模式
        return self._call_api_legacy(chunk, idx, cache_dir)"""
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        
        # 写入修改后的文件
        with open(ai_service_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已临时禁用验证功能")
        print("📝 修改了 call_api 方法，强制使用传统模式")
        return True
    else:
        print("❌ 未找到目标代码，可能文件结构已改变")
        return False

def restore_ai_service():
    """恢复ai_service.py的原始版本"""
    ai_service_path = Path("ai_service.py")
    backup_path = ai_service_path.with_suffix('.py.backup')
    
    if not backup_path.exists():
        print("❌ 备份文件不存在")
        return False
    
    # 恢复备份
    with open(backup_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    with open(ai_service_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已恢复 ai_service.py 到原始版本")
    return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python quick_fix_validation.py disable  # 禁用验证功能")
        print("  python quick_fix_validation.py restore  # 恢复原始版本")
        sys.exit(1)
    
    action = sys.argv[1].lower()
    
    if action == "disable":
        print("🔧 正在临时禁用验证功能...")
        if disable_validation_in_ai_service():
            print("\n✅ 修复完成！现在可以正常处理项目了")
            print("⚠️  这是临时修复，处理完成后建议运行 'restore' 恢复原始功能")
        else:
            print("\n❌ 修复失败")
    
    elif action == "restore":
        print("🔄 正在恢复原始版本...")
        if restore_ai_service():
            print("\n✅ 恢复完成！验证功能已重新启用")
        else:
            print("\n❌ 恢复失败")
    
    else:
        print("❌ 无效的操作，请使用 'disable' 或 'restore'")
        sys.exit(1)

if __name__ == "__main__":
    main()
